import os
import base64
import io
import asyncio
import json
import numpy as np
from fastapi import FastAPI, UploadFile, File, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from openai import OpenAI
from dotenv import load_dotenv
import openwakeword
from openwakeword.model import Model

# Import web search tool
from tools import get_web_search_tool_schema, process_tool_call
# Import Jellyfin tools
from jellyfin_tools import get_all_jellyfin_tool_schemas, process_jellyfin_tool_call

load_dotenv()

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# Initialize wake word models
print("Loading wake word models...")
wake_word_model = Model(
    wakeword_models=["VoiceModels/scarlett.onnx", "VoiceModels/skynet.onnx"],
    inference_framework="onnx"  # Use ONNX for better cross-platform support
)
print("Wake word models initialized (Scarlett + Skynet execution keyword)")

# Define available tools
AVAILABLE_TOOLS = [
    get_web_search_tool_schema()
] + get_all_jellyfin_tool_schemas()

@app.post("/api/chat")
async def chat_with_audio(audio_file: UploadFile = File(...)):
    try:
        audio_data = await audio_file.read()
        
        if len(audio_data) > 20 * 1024 * 1024:
            raise HTTPException(status_code=400, detail="Audio file too large (max 20MB)")
        
        audio_base64 = base64.b64encode(audio_data).decode('utf-8')

        # Run transcription and chat completion in parallel
        async def transcribe_audio():
            try:
                # Create a file-like object from the audio data
                audio_file_object = io.BytesIO(audio_data)
                audio_file_object.name = "audio.wav"
                
                # Run transcription in a thread pool to avoid blocking
                loop = asyncio.get_event_loop()
                transcription = await loop.run_in_executor(
                    None,
                    lambda: client.audio.transcriptions.create(
                        model="whisper-1",
                        file=audio_file_object,
                        language="en"
                    )
                )
                return transcription.text
            except Exception as e:
                raise Exception(f"Transcription failed: {str(e)}")
        
        async def get_chat_completion_with_tools():
            try:
                # Initial message with audio input
                messages = [
                    {
                        "role": "system",
                        "content": "You are a Scarlett, a helpful assistant. Always respond in English. You have access to web search capabilities and TV control through Jellyfin. You can play TV shows, control playback (pause, play, next, previous), and turn off the TV. Unless you are 100 percent sure you know all there is to know about something, make relevant web search before answering the user. Avoid ending your response with 'I hope that helps!' or any other generic closing remarks, including asking if the user has for any follow-up questions or tasks. Avoid giving your opinion. Keep it brief. Stick to facts. Avoid pleasantries. If the user asks for a non websearch tool like playing a TV show, pausing, or going to the next episode, respond with only one or two words, like 'playing', or 'paused', 'playing next', as appropriate."
                    },
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "input_audio",
                                "input_audio": {
                                    "data": audio_base64,
                                    "format": "wav"
                                }
                            }
                        ]
                    }
                ]
                
                # Run initial chat completion in a thread pool to avoid blocking
                loop = asyncio.get_event_loop()
                response = await loop.run_in_executor(
                    None,
                    lambda: client.chat.completions.create(
                        model="gpt-4o-audio-preview",
                        modalities=["text", "audio"],
                        audio={"voice": "nova", "format": "mp3"},
                        messages=messages,
                        tools=AVAILABLE_TOOLS,
                        tool_choice="auto"
                    )
                )
                
                # Check if the assistant wants to use tools
                if response.choices[0].message.tool_calls:
                    print(f"Assistant wants to use {len(response.choices[0].message.tool_calls)} tool(s)")
                    
                    # Add the assistant's message to conversation
                    messages.append({
                        "role": "assistant",
                        "content": response.choices[0].message.content,
                        "tool_calls": [
                            {
                                "id": tool_call.id,
                                "type": tool_call.type,
                                "function": {
                                    "name": tool_call.function.name,
                                    "arguments": tool_call.function.arguments
                                }
                            }
                            for tool_call in response.choices[0].message.tool_calls
                        ]
                    })
                    
                    # Process each tool call
                    for tool_call in response.choices[0].message.tool_calls:
                        print(f"Processing tool call: {tool_call.function.name}")

                        if tool_call.function.name == "web_search":
                            # Process web search tool call
                            tool_response = await process_tool_call(tool_call)
                            messages.append(tool_response)
                        elif tool_call.function.name in ["jellyfin_play_show", "jellyfin_remote_control"]:
                            # Process Jellyfin tool call
                            tool_response = await process_jellyfin_tool_call(tool_call)
                            messages.append(tool_response)
                        else:
                            # Handle unknown tool
                            messages.append({
                                "role": "tool",
                                "content": json.dumps({
                                    "error": f"Unknown tool: {tool_call.function.name}"
                                }),
                                "tool_call_id": tool_call.id
                            })

                    # Get final response with tool results
                    print("Getting final response with tool results...")
                    final_response = await loop.run_in_executor(
                        None,
                        lambda: client.chat.completions.create(
                            model="gpt-4o-audio-preview",
                            modalities=["text", "audio"],
                            audio={"voice": "nova", "format": "mp3"},
                            messages=messages
                        )
                    )

                    return final_response
                else:
                    # No tools needed, return original response
                    return response
                    
            except Exception as e:
                raise Exception(f"Chat completion failed: {str(e)}")
        
        # Execute both operations in parallel
        user_transcript, chat_response = await asyncio.gather(
            transcribe_audio(),
            get_chat_completion_with_tools()
        )

        if chat_response.choices[0].message.audio:
            audio_response = chat_response.choices[0].message.audio.data
            assistant_transcript = chat_response.choices[0].message.audio.transcript

            return JSONResponse({
                "audio_data": audio_response,
                "transcript": assistant_transcript,
                "user_transcript": user_transcript
            })
        else:
            raise HTTPException(status_code=500, detail="No audio response received")
            
    except Exception as e:
        print(f"Error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {"status": "ok"}

@app.websocket("/ws/wakeword")
async def websocket_wakeword(websocket: WebSocket):
    await websocket.accept()
    print("WebSocket connection established for wake word detection")

    # Track listening state for execution keyword
    is_listening_after_wakeword = False

    try:
        while True:
            # Receive audio data from the client
            data = await websocket.receive_text()
            message = json.loads(data)

            if message["type"] == "audio":
                # Convert base64 audio to numpy array
                audio_bytes = base64.b64decode(message["data"])
                audio_array = np.frombuffer(audio_bytes, dtype=np.int16)

                # Ensure audio is mono and 16kHz for openWakeWord
                # The frontend will send 16kHz mono audio

                # Run wake word detection
                prediction = wake_word_model.predict(audio_array)

                # Check if wake word was detected
                # prediction is a dict with model names as keys and scores as values
                for model_name, score in prediction.items():
                    if score > 0.5:  # Threshold for detection
                        # Handle Scarlett (primary wake word) - always active
                        if "scarlett" in model_name.lower():
                            print(f"Primary wake word detected: {model_name} (score: {score})")
                            is_listening_after_wakeword = True
                            await websocket.send_text(json.dumps({
                                "type": "wakeword_detected",
                                "model": model_name,
                                "score": float(score)
                            }))
                            break

                        # Handle Skynet (execution keyword) - only when actively listening
                        elif "skynet" in model_name.lower() and is_listening_after_wakeword:
                            print(f"Execution keyword detected: {model_name} (score: {score})")
                            is_listening_after_wakeword = False  # Reset state
                            await websocket.send_text(json.dumps({
                                "type": "execution_keyword_detected",
                                "model": model_name,
                                "score": float(score)
                            }))
                            break

            elif message["type"] == "ping":
                # Keep connection alive
                await websocket.send_text(json.dumps({"type": "pong"}))

            elif message["type"] == "listening_state":
                # Update listening state from frontend
                is_listening_after_wakeword = message.get("is_listening", False)

    except WebSocketDisconnect:
        print("WebSocket disconnected")
    except Exception as e:
        print(f"WebSocket error: {e}")
        await websocket.close()

# Mount the frontend directory AFTER defining all API and WebSocket routes
frontend_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "frontend")
if os.path.exists(frontend_path):
    app.mount("/", StaticFiles(directory=frontend_path, html=True), name="frontend")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)