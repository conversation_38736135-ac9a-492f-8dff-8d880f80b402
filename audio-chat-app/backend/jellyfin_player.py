#!/usr/bin/env python3
"""
Minimal Jellyfin TV Player
Connects to LG TV, launches Jellyfin, and plays episodes based on user input
"""

from pywebostv.connection import WebOSClient
from pywebostv.controls import ApplicationControl, InputControl, SystemControl
from wakeonlan import send_magic_packet
import json
import time
import requests
import argparse
import re

class JellyfinTVPlayer:
    def __init__(self):
        # Load configurations
        with open('lgtv_key.json', 'r') as f:
            self.tv_key = json.load(f)

        with open('jellyfin_config.json', 'r') as f:
            jellyfin_config = json.load(f)
            self.jellyfin_url = jellyfin_config['server']
            self.api_key = jellyfin_config['api_key']

        self.tv_ip = '*************'
        self.mac_address = '7c:64:6c:c7:ed:a7'  # LG TV MAC address for Wake-on-LAN
        self.client = None
        self.connected = False
    
    def connect_to_tv(self):
        """Connect to LG TV"""
        try:
            print("Connecting to TV...")
            self.client = WebOSClient(self.tv_ip, secure=True)
            self.client.connect()
            
            # Register with saved key
            for status in self.client.register(self.tv_key):
                if status == WebOSClient.PROMPTED:
                    print("Please accept the pairing request on your TV")
                elif status == WebOSClient.REGISTERED:
                    print("✓ Connected to TV successfully")
            
            self.connected = True
            return True
        except Exception as e:
            print(f"❌ Failed to connect to TV: {e}")
            return False

    def connect_to_tv_quick(self):
        """Try to connect to TV quickly (for when TV might already be on)"""
        import threading

        print("🔗 Checking if TV is already on...")

        # Use threading to implement timeout
        connection_successful = threading.Event()
        connection_failed = threading.Event()

        def try_connect():
            try:
                self.client = WebOSClient(self.tv_ip, secure=True)
                self.client.connect()

                # Register with saved key
                for status in self.client.register(self.tv_key):
                    if status == WebOSClient.PROMPTED:
                        print("Please accept the pairing request on your TV")
                    elif status == WebOSClient.REGISTERED:
                        print("✅ TV is already on and connected!")

                self.connected = True
                connection_successful.set()

            except Exception:
                connection_failed.set()

        # Start connection attempt in separate thread
        connect_thread = threading.Thread(target=try_connect)
        connect_thread.daemon = True
        connect_thread.start()

        # Wait for either success, failure, or timeout (5 seconds)
        if connection_successful.wait(timeout=5):
            return True
        else:
            # Connection failed or timed out
            if self.client:
                try:
                    self.client.close()
                except:
                    pass
                self.client = None
            return False

    def wake_tv(self):
        """Turn on TV using Wake-on-LAN"""
        if not self.mac_address:
            print("❌ MAC address not configured. Cannot wake TV.")
            return False

        try:
            print(f"📺 Sending Wake-on-LAN packet to {self.mac_address}...")
            send_magic_packet(self.mac_address)
            print("✓ Wake packet sent. Waiting for TV to turn on...")

            # Wait for TV to turn on (reduced from 15 to 10 seconds for faster startup)
            for i in range(10, 0, -1):
                print(f"\rWaiting... {i} seconds", end='', flush=True)
                time.sleep(1)

            print("\n✓ TV should be ready now")
            return True

        except Exception as e:
            print(f"❌ Wake-on-LAN failed: {e}")
            return False

    def launch_jellyfin(self):
        """Launch Jellyfin app on TV"""
        if not self.connected:
            print("Not connected to TV")
            return False
        
        try:
            print("Launching Jellyfin...")
            app_control = ApplicationControl(self.client)
            
            # Get all apps to find Jellyfin
            apps = app_control.list_apps()
            print(f"Found {len(apps)} apps on TV")
            
            # Debug: print first app structure
            if apps:
                print(f"Debug - First app structure: {apps[0]}")
                print(f"Debug - App type: {type(apps[0])}")
                if hasattr(apps[0], '__dict__'):
                    print(f"Debug - App attributes: {apps[0].__dict__}")
            
            jellyfin_app = None
            
            # Access app info from the data dictionary
            for i, app in enumerate(apps):
                app_title = ''
                app_id = ''
                
                # Access the data attribute
                if hasattr(app, 'data') and isinstance(app.data, dict):
                    app_title = app.data.get('title', '')
                    app_id = app.data.get('id', '')
                
                # Only print apps with visible titles
                if app_title and i < 20:
                    print(f"  - {app_title} (ID: {app_id})")
                
                if app_title and 'jellyfin' in app_title.lower():
                    jellyfin_app = app
                    print(f"Found Jellyfin: {app_title} (ID: {app_id})")
                    break
            
            if jellyfin_app:
                app_control.launch(jellyfin_app)
                print("✓ Jellyfin launched")
                time.sleep(5)  # Wait for app to load
                return True
            else:
                print("❌ Jellyfin app not found on TV")
                return False
                
        except Exception as e:
            print(f"❌ Failed to launch Jellyfin: {e}")
            return False
    
    def search_shows(self, query, auto_select=False):
        """Search for TV shows in Jellyfin"""
        try:
            # Search for series matching the query
            search_url = f"{self.jellyfin_url}/Items"
            params = {
                'searchTerm': query,
                'IncludeItemTypes': 'Series',
                'Recursive': True,
                'api_key': self.api_key
            }

            response = requests.get(search_url, params=params)
            response.raise_for_status()

            items = response.json().get('Items', [])

            if not items:
                print(f"❌ No shows found matching '{query}'")
                return None

            # Show matches
            print(f"\n🔍 Found {len(items)} show(s):")
            for i, show in enumerate(items):
                print(f"  {i+1}. {show['Name']}")

            if len(items) == 1 or auto_select:
                selected = items[0]
                if len(items) > 1:
                    print(f"📺 Auto-selecting first match: {selected['Name']}")
                return selected
            else:
                # Let user choose
                choice = input("\nSelect show number (or press Enter for first): ").strip()
                if choice.isdigit() and 1 <= int(choice) <= len(items):
                    return items[int(choice) - 1]
                else:
                    return items[0]

        except Exception as e:
            print(f"❌ Error searching shows: {e}")
            return None
    
    def get_random_episode(self, series_id):
        """Get a random episode from the series"""
        try:
            # Get all episodes from the series
            episodes_url = f"{self.jellyfin_url}/Shows/{series_id}/Episodes"
            params = {
                'api_key': self.api_key,
                'Fields': 'Overview,Path'
            }
            
            response = requests.get(episodes_url, params=params)
            response.raise_for_status()
            
            episodes = response.json().get('Items', [])
            
            if not episodes:
                print("No episodes found")
                return None
            
            # Pick a random episode
            import random
            episode = random.choice(episodes)
            
            season = episode.get('ParentIndexNumber', '?')
            ep_num = episode.get('IndexNumber', '?')
            print(f"\nSelected: S{season:02d}E{ep_num:02d} - {episode['Name']}")
            
            return episode
            
        except Exception as e:
            print(f"❌ Error getting episodes: {e}")
            return None

    def get_next_episode(self, series_id):
        """Get the next unwatched episode or resume episode from the series"""
        try:
            # Get all episodes from the series with UserData to check watch status
            episodes_url = f"{self.jellyfin_url}/Shows/{series_id}/Episodes"
            params = {
                'api_key': self.api_key,
                'Fields': 'Overview,Path,UserData,ParentIndexNumber,IndexNumber'
            }

            response = requests.get(episodes_url, params=params)
            response.raise_for_status()

            episodes = response.json().get('Items', [])

            if not episodes:
                print("No episodes found")
                return None

            # Sort episodes by season and episode number
            episodes.sort(key=lambda ep: (ep.get('ParentIndexNumber', 0), ep.get('IndexNumber', 0)))

            # First, look for episodes with partial progress (resume points)
            for episode in episodes:
                user_data = episode.get('UserData', {})
                play_count = user_data.get('PlayCount', 0)
                playback_position = user_data.get('PlaybackPositionTicks', 0)

                # If episode has progress but isn't fully watched, resume it
                if playback_position > 0 and play_count == 0:
                    season = episode.get('ParentIndexNumber', '?')
                    ep_num = episode.get('IndexNumber', '?')
                    print(f"\n📺 Resuming: S{season:02d}E{ep_num:02d} - {episode['Name']}")
                    return episode

            # If no resume points, find first unwatched episode
            for episode in episodes:
                user_data = episode.get('UserData', {})
                play_count = user_data.get('PlayCount', 0)

                if play_count == 0:  # Unwatched episode
                    season = episode.get('ParentIndexNumber', '?')
                    ep_num = episode.get('IndexNumber', '?')
                    print(f"\n▶️  Next episode: S{season:02d}E{ep_num:02d} - {episode['Name']}")
                    return episode

            # If all episodes are watched, start from the beginning
            first_episode = episodes[0]
            season = first_episode.get('ParentIndexNumber', '?')
            ep_num = first_episode.get('IndexNumber', '?')
            print(f"\n🔄 All episodes watched, restarting from: S{season:02d}E{ep_num:02d} - {first_episode['Name']}")
            return first_episode

        except Exception as e:
            print(f"❌ Error getting next episode: {e}")
            return None

    def parse_episode_identifier(self, episode_str):
        """Parse episode identifier into season and episode numbers"""
        episode_str = episode_str.strip().lower()

        # Pattern 1: S01E05, s1e5, S1E5, etc.
        match = re.match(r's(\d+)e(\d+)', episode_str)
        if match:
            return int(match.group(1)), int(match.group(2))

        # Pattern 2: 1x5, 12x3, etc.
        match = re.match(r'(\d+)x(\d+)', episode_str)
        if match:
            return int(match.group(1)), int(match.group(2))

        # Pattern 3: 1.5, 12.3, etc.
        match = re.match(r'(\d+)\.(\d+)', episode_str)
        if match:
            return int(match.group(1)), int(match.group(2))

        return None, None

    def get_specific_episode(self, series_id, season_num, episode_num):
        """Get a specific episode by season and episode number"""
        try:
            # Get all episodes from the series
            episodes_url = f"{self.jellyfin_url}/Shows/{series_id}/Episodes"
            params = {
                'api_key': self.api_key,
                'Fields': 'Overview,Path,ParentIndexNumber,IndexNumber'
            }

            response = requests.get(episodes_url, params=params)
            response.raise_for_status()

            episodes = response.json().get('Items', [])

            if not episodes:
                print("No episodes found for this series")
                return None

            # Find the specific episode
            for episode in episodes:
                ep_season = episode.get('ParentIndexNumber')
                ep_number = episode.get('IndexNumber')

                if ep_season == season_num and ep_number == episode_num:
                    print(f"\n✅ Found: S{ep_season:02d}E{ep_number:02d} - {episode['Name']}")
                    return episode

            # Episode not found - show available episodes
            print(f"\n❌ Episode S{season_num:02d}E{episode_num:02d} not found")
            self.show_available_episodes(episodes, season_num)
            return None

        except Exception as e:
            print(f"❌ Error getting specific episode: {e}")
            return None

    def show_available_episodes(self, episodes, target_season=None):
        """Show available episodes, optionally filtered by season"""
        if target_season:
            season_episodes = [ep for ep in episodes if ep.get('ParentIndexNumber') == target_season]
            if season_episodes:
                print(f"\nAvailable episodes in Season {target_season}:")
                for ep in season_episodes[:10]:  # Show first 10
                    season = ep.get('ParentIndexNumber', '?')
                    ep_num = ep.get('IndexNumber', '?')
                    print(f"  S{season:02d}E{ep_num:02d} - {ep['Name']}")
                if len(season_episodes) > 10:
                    print(f"  ... and {len(season_episodes) - 10} more episodes")
            else:
                print(f"\nNo episodes found in Season {target_season}")
                # Show available seasons
                seasons = sorted(set(ep.get('ParentIndexNumber', 0) for ep in episodes if ep.get('ParentIndexNumber')))
                print(f"Available seasons: {', '.join(map(str, seasons))}")
        else:
            # Group by season
            seasons = {}
            for ep in episodes:
                season = ep.get('ParentIndexNumber', 0)
                if season not in seasons:
                    seasons[season] = []
                seasons[season].append(ep)

            print(f"\nAvailable seasons and episodes:")
            for season in sorted(seasons.keys()):
                print(f"  Season {season}: {len(seasons[season])} episodes")

    def get_jellyfin_sessions(self):
        """Get active Jellyfin sessions"""
        try:
            sessions_url = f"{self.jellyfin_url}/Sessions"
            params = {'api_key': self.api_key}

            response = requests.get(sessions_url, params=params)
            response.raise_for_status()

            sessions = response.json()
            print(f"🔍 Found {len(sessions)} active sessions:")
            for i, session in enumerate(sessions):
                client_name = session.get('Client', 'Unknown')
                device_name = session.get('DeviceName', 'Unknown')
                session_id = session.get('Id', 'Unknown')
                user_name = session.get('UserName', 'Unknown')
                print(f"  {i+1}. {client_name} on {device_name} (User: {user_name}) - ID: {session_id}")
            return sessions

        except Exception as e:
            print(f"❌ Error getting sessions: {e}")
            return []

    def find_tv_session(self, sessions=None):
        """Find the TV session from active sessions"""
        if sessions is None:
            sessions = self.get_jellyfin_sessions()

        # Look for LG TV or Jellyfin Web client
        for session in sessions:
            client_name = session.get('Client', '').lower()
            device_name = session.get('DeviceName', '').lower()

            # Check for LG TV or Jellyfin Web
            if ('lg' in device_name and 'tv' in device_name):
                print(f"📺 Found TV session: {session.get('Client')} on {session.get('DeviceName')}")
                return session.get('Id')

        # If no specific match, return the first session (fallback)
        if sessions:
            print(f"⚠️  No TV session found, using first available session")
            return sessions[0].get('Id')

        return None

    def play_episode(self, episode_id):
        """Use Jellyfin remote control API to play the episode"""
        try:
            print(f"🎬 Starting playback for episode...")

            # Get TV session
            session_id = self.find_tv_session()
            if not session_id:
                print("❌ No TV session found - make sure Jellyfin is running on your TV")
                return False

            # Use proper remote control API - try query parameters instead of JSON body
            playback_url = f"{self.jellyfin_url}/Sessions/{session_id}/Playing"
            params = {
                'api_key': self.api_key,
                'itemIds': episode_id,  # Try single item first
                'playCommand': 'PlayNow',
                'startPositionTicks': 0
            }

            print(f"📡 API Call: POST {playback_url}")
            print(f"📦 Query Params: {params}")
            print(f"🎯 Session ID: {session_id}")

            response = requests.post(playback_url, params=params)

            print(f"📊 Response: {response.status_code}")
            if response.status_code == 204:
                print("✅ Playback started successfully!")
                return True
            elif response.status_code == 404:
                print("❌ Session not found - TV may be disconnected")
                print("Try launching Jellyfin on your TV first")
                return False
            elif response.status_code == 400:
                print(f"❌ Bad request (400). Trying alternative format...")
                if response.text:
                    print(f"Error details: {response.text}")

                # Try alternative format with JSON body
                print("🔄 Trying JSON body format...")
                json_data = {
                    "ItemIds": [episode_id],
                    "PlayCommand": "PlayNow",
                    "StartPositionTicks": 0
                }
                response2 = requests.post(playback_url, json=json_data, params={'api_key': self.api_key})
                print(f"📊 Alternative Response: {response2.status_code}")

                if response2.status_code == 204:
                    print("✅ Playback started successfully with alternative format!")
                    return True
                else:
                    print(f"❌ Alternative format also failed: {response2.status_code}")
                    if response2.text:
                        print(f"Response: {response2.text}")
                return False
            else:
                print(f"❌ Playback failed: {response.status_code}")
                if response.text:
                    print(f"Response: {response.text}")
                return False

        except Exception as e:
            print(f"❌ Error starting playback: {e}")
            print("\nDebugging info:")
            print(f"Episode ID: {episode_id}")
            print(f"Jellyfin URL: {self.jellyfin_url}")
            print(f"API Key: {self.api_key[:8]}...")
            return False

    def pause_playback(self):
        """Pause current playback"""
        try:
            session_id = self.find_tv_session()
            if not session_id:
                print("❌ No TV session found - make sure Jellyfin is running on your TV")
                return False

            pause_url = f"{self.jellyfin_url}/Sessions/{session_id}/Playing/Pause"
            params = {'api_key': self.api_key}

            print("📡 Sending pause command to Jellyfin...")
            response = requests.post(pause_url, params=params)

            if response.status_code == 204:
                print("✅ Playback paused successfully!")
                return True
            else:
                print(f"❌ Pause failed: {response.status_code}")
                if response.text:
                    print(f"Response: {response.text}")
                return False

        except Exception as e:
            print(f"❌ Error pausing playback: {e}")
            return False

    def resume_playback(self):
        """Resume/play current playback"""
        try:
            session_id = self.find_tv_session()
            if not session_id:
                print("❌ No TV session found - make sure Jellyfin is running on your TV")
                return False

            unpause_url = f"{self.jellyfin_url}/Sessions/{session_id}/Playing/Unpause"
            params = {'api_key': self.api_key}

            print("📡 Sending play/resume command to Jellyfin...")
            response = requests.post(unpause_url, params=params)

            if response.status_code == 204:
                print("✅ Playback resumed successfully!")
                return True
            else:
                print(f"❌ Resume failed: {response.status_code}")
                if response.text:
                    print(f"Response: {response.text}")
                return False

        except Exception as e:
            print(f"❌ Error resuming playback: {e}")
            return False

    def next_track(self):
        """Skip to next episode/track"""
        try:
            session_id = self.find_tv_session()
            if not session_id:
                print("❌ No TV session found - make sure Jellyfin is running on your TV")
                return False

            next_url = f"{self.jellyfin_url}/Sessions/{session_id}/Playing/NextTrack"
            params = {'api_key': self.api_key}

            print("📡 Sending next track command to Jellyfin...")
            response = requests.post(next_url, params=params)

            if response.status_code == 204:
                print("✅ Skipped to next episode!")
                return True
            else:
                print(f"❌ Next track failed: {response.status_code}")
                if response.text:
                    print(f"Response: {response.text}")
                return False

        except Exception as e:
            print(f"❌ Error skipping to next: {e}")
            return False

    def previous_track(self):
        """Go to previous episode/track"""
        try:
            session_id = self.find_tv_session()
            if not session_id:
                print("❌ No TV session found - make sure Jellyfin is running on your TV")
                return False

            prev_url = f"{self.jellyfin_url}/Sessions/{session_id}/Playing/PreviousTrack"
            params = {'api_key': self.api_key}

            print("📡 Sending previous track command to Jellyfin...")
            response = requests.post(prev_url, params=params)

            if response.status_code == 204:
                print("✅ Went to previous episode!")
                return True
            else:
                print(f"❌ Previous track failed: {response.status_code}")
                if response.text:
                    print(f"Response: {response.text}")
                return False

        except Exception as e:
            print(f"❌ Error going to previous: {e}")
            return False

    def turn_off_tv(self):
        """Turn off the TV"""
        if not self.connect_to_tv_quick():
            print("❌ Cannot connect to TV to turn it off")
            return False

        try:
            print("📺 Turning off TV...")
            system = SystemControl(self.client)
            system.power_off()
            print("✅ TV turned off")
            return True
        except Exception as e:
            print(f"❌ Failed to turn off TV: {e}")
            return False
        finally:
            if self.client:
                try:
                    self.client.close()
                except:
                    pass
                self.client = None
                self.connected = False

    def test_sessions(self):
        """Test method to check session discovery"""
        print("=== Testing Jellyfin Sessions ===\n")
        sessions = self.get_jellyfin_sessions()
        if sessions:
            tv_session = self.find_tv_session(sessions)
            if tv_session:
                print(f"\n✅ TV Session found: {tv_session}")
            else:
                print(f"\n❌ No TV session identified")
        else:
            print("❌ No active sessions found")

    def run(self, show_name=None, episode_identifier=None):
        """Main execution flow"""
        print("=== Jellyfin TV Player ===\n")

        # Try to connect to TV immediately (efficient approach)
        if self.connect_to_tv_quick():
            # TV was already on, continue normally
            pass
        else:
            # TV appears to be off, use Wake-on-LAN
            print("� TV appears to be off, turning it on...")
            if not self.wake_tv():
                print("❌ Failed to send Wake-on-LAN packet")
                return

            # Try connecting again after Wake-on-LAN
            print("\n🔗 Connecting to TV after wake-up...")
            if not self.connect_to_tv():
                print("\n❌ Failed to connect to TV even after Wake-on-LAN")
                print("\n🔧 Troubleshooting suggestions:")
                print(f"   • Check TV IP address: {self.tv_ip}")
                print(f"   • Check TV MAC address: {self.mac_address}")
                print("   • Ensure TV and computer are on the same network")
                print("   • Verify Wake-on-LAN is enabled in TV settings")
                print("   • Try manually turning on the TV and running the script again")
                return

        try:
            # Launch Jellyfin
            if not self.launch_jellyfin():
                return

            # Check for active sessions
            print("\n" + "="*50)
            print("Checking for active Jellyfin sessions...")
            self.test_sessions()
            print("="*50)

            # Get show to play
            if show_name:
                show_query = show_name
                print(f"\n🎯 Searching for: {show_query}")
            else:
                show_query = input("\nWhat show would you like to watch? ").strip()

                if not show_query:
                    print("No show specified")
                    return

            # Search for the show
            auto_select = show_name is not None  # Auto-select when using command line
            show = self.search_shows(show_query, auto_select=auto_select)
            if not show:
                return

            print(f"\n✅ Selected: {show['Name']}")

            # Get episode to play
            if episode_identifier:
                # Specific episode requested
                season_num, episode_num = self.parse_episode_identifier(episode_identifier)

                if season_num is None or episode_num is None:
                    print(f"❌ Invalid episode format: '{episode_identifier}'")
                    print("Supported formats:")
                    print("  - S01E05 or s1e5 (season 1, episode 5)")
                    print("  - 1x5 (season 1, episode 5)")
                    print("  - 1.5 (season 1, episode 5)")
                    return

                print(f"🎯 Looking for Season {season_num}, Episode {episode_num}")
                episode = self.get_specific_episode(show['Id'], season_num, episode_num)

            elif show_name:
                # Show-only mode: get next/resume episode using Jellyfin's watch history
                print("🎬 Auto-play mode: Finding next episode based on watch history...")
                episode = self.get_next_episode(show['Id'])

            else:
                # Interactive mode: get a random episode (original behavior)
                episode = self.get_random_episode(show['Id'])

            if not episode:
                return

            # Play the episode using the same API we use for specific episodes
            self.play_episode(episode['Id'])
            
        except KeyboardInterrupt:
            print("\n\nInterrupted by user")
        except Exception as e:
            print(f"\n❌ Unexpected error: {e}")
        finally:
            if self.client:
                print("\nDisconnecting from TV...")
                self.client.close()

def main():
    """Main function with argument parsing"""
    parser = argparse.ArgumentParser(
        description='Jellyfin TV Player - Automatically play TV shows on your LG Smart TV',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                                    # Interactive mode
  %(prog)s "Breaking Bad"                     # Auto-play next/resume episode
  %(prog)s "Trailer Park Boys" "S12E08"       # Play specific episode
  %(prog)s "Breaking Bad" "3x7"               # Alternative format
  %(prog)s "The Office" "2.5"                 # Another format
  %(prog)s test-sessions                      # Test session discovery only

Remote control commands:
  %(prog)s --pause                            # Pause current playback
  %(prog)s --play                             # Resume/play current playback
  %(prog)s --next                             # Skip to next episode
  %(prog)s --previous                         # Go to previous episode
  %(prog)s --off                              # Turn off TV

Episode formats supported (when specifying specific episode):
  - S01E05, s1e5, S1E5    (season 1, episode 5)
  - 1x5, 12x3             (season 1, episode 5)
  - 1.5, 12.3             (season 1, episode 5)
        """
    )

    parser.add_argument(
        'show_name',
        nargs='?',
        help='Name of the TV series to search for'
    )
    parser.add_argument(
        'episode',
        nargs='?',
        help='Episode identifier (e.g., S01E05, 1x5, 1.5)'
    )

    # Remote control commands
    parser.add_argument('--pause', action='store_true', help='Pause current playback')
    parser.add_argument('--play', action='store_true', help='Resume/play current playback')
    parser.add_argument('--next', action='store_true', help='Skip to next episode')
    parser.add_argument('--previous', action='store_true', help='Go to previous episode')
    parser.add_argument('--off', action='store_true', help='Turn off TV')

    args = parser.parse_args()

    player = JellyfinTVPlayer()

    # Handle remote control commands
    if args.pause:
        player.pause_playback()
        return

    if args.play:
        player.resume_playback()
        return

    if args.next:
        player.next_track()
        return

    if args.previous:
        player.previous_track()
        return

    if args.off:
        player.turn_off_tv()
        return

    # Handle special test-sessions command
    if args.show_name == "test-sessions":
        player.test_sessions()
        return

    # Validate arguments
    if args.episode and not args.show_name:
        print("❌ Error: When specifying an episode, you must also provide a show name")
        parser.print_help()
        return

    # Run the player
    try:
        player.run(args.show_name, args.episode)
    except KeyboardInterrupt:
        print("\n\n⏹️  Interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")

if __name__ == "__main__":
    main()